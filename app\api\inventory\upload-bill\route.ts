import { NextRequest, NextResponse } from 'next/server';
import { query, run } from '@/lib/db';

// 导入码上放心SDK
const ApiClient = require('../../../../mashangfangxin/index.js').ApiClient;

interface Setting {
  setting_name: string;
  setting_value: string;
}

/**
 * 从系统设置中获取码上放心开放平台配置
 */
async function getConfig() {
  try {
    const settings = await query('SELECT setting_name, setting_value FROM settings WHERE setting_name IN (?, ?, ?, ?)', [
      'mashangfangxinAppkey',
      'mashangfangxinAppsecret',
      'mashangfangxinUrl',
      'mashangfangxinRefEntId'
    ]) as Setting[];

    const config = {
      appkey: '',
      appsecret: '',
      url: 'http://gw.api.taobao.com/router/rest',
      ref_ent_id: ''
    };

    settings.forEach(setting => {
      switch (setting.setting_name) {
        case 'mashangfangxinAppkey':
          config.appkey = setting.setting_value;
          break;
        case 'mashangfangxinAppsecret':
          config.appsecret = setting.setting_value;
          break;
        case 'mashangfangxinUrl':
          config.url = setting.setting_value;
          break;
        case 'mashangfangxinRefEntId':
          config.ref_ent_id = setting.setting_value;
          break;
      }
    });

    // 使用环境变量作为备用
    if (!config.appkey) {
      config.appkey = process.env.MASHANGFANGXIN_APPKEY || '';
    }
    if (!config.appsecret) {
      config.appsecret = process.env.MASHANGFANGXIN_APPSECRET || '';
    }
    if (!config.url) {
      config.url = process.env.MASHANGFANGXIN_URL || 'http://gw.api.taobao.com/router/rest';
    }
    if (!config.ref_ent_id) {
      config.ref_ent_id = process.env.MASHANGFANGXIN_REF_ENT_ID || '';
    }

    return config;
  } catch (error) {
    console.error('获取码上放心配置失败:', error);
    return {
      appkey: process.env.MASHANGFANGXIN_APPKEY || '',
      appsecret: process.env.MASHANGFANGXIN_APPSECRET || '',
      url: process.env.MASHANGFANGXIN_URL || 'http://gw.api.taobao.com/router/rest',
      ref_ent_id: process.env.MASHANGFANGXIN_REF_ENT_ID || ''
    };
  }
}

/**
 * 创建API客户端实例
 */
function createClient(config: any) {
  return new ApiClient({
    'appkey': config.appkey,
    'appsecret': config.appsecret,
    'url': config.url
  });
}

/**
 * 生成单据编号
 */
function generateBillNumber(type: 'IN' | 'OUT'): string {
  const now = new Date();
  const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '');
  const timeStr = now.toTimeString().slice(0, 8).replace(/:/g, '');
  const randomStr = Math.random().toString(36).substring(2, 8).toUpperCase();
  return `${type}-${dateStr}${timeStr}-${randomStr}`;
}

/**
 * 上传出入库单据到码上放心平台
 */
async function uploadBillToMashangfangxin(config: any, billData: any) {
  const client = createClient(config);
  
  return new Promise((resolve, reject) => {
    // 根据单据类型选择API接口
    const apiMethod = 'alibaba.alihealth.drugtrace.top.lsyd.uploadinoutbill';
    
    console.log('上传出入库单据到码上放心平台，参数:', {
      bill_code: billData.bill_code,
      bill_type: billData.bill_type,
      bill_time: billData.bill_time,
      ref_ent_id: config.ref_ent_id,
      drug_detail_list: billData.drug_detail_list
    });

    client.execute(apiMethod, {
      bill_code: billData.bill_code,
      bill_type: billData.bill_type,
      bill_time: billData.bill_time,
      ref_ent_id: config.ref_ent_id,
      drug_detail_list: JSON.stringify(billData.drug_detail_list)
    }, function(error: any, response: any) {
      if (error) {
        console.error('上传出入库单据失败:', error);
        reject(error);
      } else {
        console.log('上传出入库单据成功，响应数据:', JSON.stringify(response, null, 2));
        resolve(response);
      }
    });
  });
}

/**
 * POST 请求处理函数 - 上传出入库单据
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { type, inventory_id, trace_codes } = body;

    if (!type || !inventory_id) {
      return NextResponse.json({
        success: false,
        message: '缺少必要参数：type 和 inventory_id'
      }, { status: 400 });
    }

    if (!trace_codes || !Array.isArray(trace_codes) || trace_codes.length === 0) {
      return NextResponse.json({
        success: false,
        message: '缺少药品追溯码信息'
      }, { status: 400 });
    }

    // 获取码上放心平台配置
    const config = await getConfig();
    
    if (!config.appkey || !config.appsecret || !config.ref_ent_id) {
      return NextResponse.json({
        success: false,
        message: '码上放心平台配置不完整，请在系统设置中配置相关参数'
      }, { status: 400 });
    }

    // 获取库存记录详情
    const inventoryRecord = await query(
      `SELECT 
        i.*,
        p.名称 as product_name,
        p.通用名 as generic_name,
        p.规格 as specification,
        p.生产厂家 as manufacturer,
        p.批准文号 as approval_number,
        s.名称 as supplier_name
      FROM 库存记录 i
      LEFT JOIN 药品信息 p ON i.药品编号 = p.编号
      LEFT JOIN 供应商 s ON i.供应商编号 = s.编号
      WHERE i.编号 = ?`,
      [inventory_id]
    );

    if (!inventoryRecord || inventoryRecord.length === 0) {
      return NextResponse.json({
        success: false,
        message: '库存记录不存在'
      }, { status: 404 });
    }

    const record = inventoryRecord[0];
    
    // 生成单据编号
    const billCode = generateBillNumber(type === 'in' ? 'IN' : 'OUT');
    
    // 构建单据数据
    const billData = {
      bill_code: billCode,
      bill_type: type === 'in' ? '101' : '102', // 101-入库，102-出库
      bill_time: record.操作时间 || new Date().toISOString(),
      drug_detail_list: trace_codes.map((traceCode: string) => ({
        drug_ent_base_info: {
          ent_name: record.manufacturer || '',
          approval_licence_no: record.approval_number || '',
          physic_type: '普通药品' // 添加必需的药品类型字段
        },
        drug_info: {
          drug_name: record.product_name || '',
          drug_spec: record.specification || '',
          drug_form: record.剂型 || '',
          drug_approval_no: record.approval_number || ''
        },
        code_info: {
          code: traceCode,
          pkg_amount: 1
        },
        supplier_info: type === 'in' ? {
          ent_name: record.supplier_name || '',
          ent_id: record.供应商编号 || ''
        } : undefined
      }))
    };

    try {
      // 上传到码上放心平台
      const uploadResult = await uploadBillToMashangfangxin(config, billData);
      
      // 记录上传状态到数据库
      await run(
        `UPDATE 库存记录 SET 
          码上放心单据号 = ?,
          码上放心上传状态 = ?,
          码上放心上传时间 = CURRENT_TIMESTAMP,
          码上放心响应 = ?
        WHERE 编号 = ?`,
        [billCode, 'success', JSON.stringify(uploadResult), inventory_id]
      );

      return NextResponse.json({
        success: true,
        message: '出入库单据上传成功',
        data: {
          bill_code: billCode,
          upload_result: uploadResult,
          trace_codes_count: trace_codes.length
        }
      });

    } catch (uploadError) {
      console.error('上传到码上放心平台失败:', uploadError);
      
      // 记录失败状态到数据库
      await run(
        `UPDATE 库存记录 SET 
          码上放心单据号 = ?,
          码上放心上传状态 = ?,
          码上放心上传时间 = CURRENT_TIMESTAMP,
          码上放心响应 = ?
        WHERE 编号 = ?`,
        [billCode, 'failed', JSON.stringify({ error: uploadError.message }), inventory_id]
      );

      return NextResponse.json({
        success: false,
        message: '上传到码上放心平台失败: ' + (uploadError.message || '未知错误'),
        data: {
          bill_code: billCode,
          error: uploadError.message
        }
      }, { status: 500 });
    }

  } catch (error) {
    console.error('处理出入库单据上传请求失败:', error);
    return NextResponse.json({
      success: false,
      message: '处理请求失败: ' + (error instanceof Error ? error.message : '未知错误')
    }, { status: 500 });
  }
}
