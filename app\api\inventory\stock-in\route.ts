import { NextRequest, NextResponse } from 'next/server';
import { query, run } from '@/lib/db';

interface StockInRequest {
  product_id: number;
  quantity: number;
  batch_number?: string;
  supplier_id?: number;
  cost_price?: number;
  expiry_date?: string;
  stock_in_date: string;
  notes?: string;
  trace_codes?: string[];
  upload_to_mashangfangxin?: boolean;
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.json() as StockInRequest;
    const {
      product_id,
      quantity,
      batch_number,
      supplier_id,
      cost_price,
      expiry_date,
      stock_in_date,
      notes,
      trace_codes,
      upload_to_mashangfangxin
    } = body;

    // 验证数据
    if (!product_id || !quantity || !stock_in_date) {
      return NextResponse.json({
        success: false,
        message: '缺少必要参数：product_id, quantity, stock_in_date'
      }, { status: 400 });
    }

    if (quantity <= 0) {
      return NextResponse.json({
        success: false,
        message: '入库数量必须大于0'
      }, { status: 400 });
    }

    // 1. 获取药品信息
    const productResult = await query(
      'SELECT 编号, 名称, 库存数量 FROM 药品信息 WHERE 编号 = ?',
      [product_id]
    );

    if (!productResult || productResult.length === 0) {
      return NextResponse.json({
        success: false,
        message: '未找到指定的药品'
      }, { status: 404 });
    }

    const product = productResult[0];
    const currentStock = product.库存数量 || 0;
    const newStock = currentStock + quantity;

    // 2. 创建库存记录
    const stockInResult = await run(
      `INSERT INTO 库存记录 (
        药品编号, 操作类型, 数量变化, 操作前库存, 操作后库存,
        供应商编号, 批次号, 有效期, 成本价, 备注, 操作时间
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        product_id,
        '入库',
        quantity,
        currentStock,
        newStock,
        supplier_id || null,
        batch_number || null,
        expiry_date || null,
        cost_price || null,
        notes || null,
        stock_in_date
      ]
    );

    if (!stockInResult.lastID) {
      return NextResponse.json({
        success: false,
        message: '创建库存记录失败'
      }, { status: 500 });
    }

    const inventoryId = stockInResult.lastID;

    // 3. 更新药品库存
    await run(
      'UPDATE 药品信息 SET 库存数量 = ? WHERE 编号 = ?',
      [newStock, product_id]
    );

    // 4. 如果有追溯码，保存追溯码记录
    if (trace_codes && trace_codes.length > 0) {
      for (const traceCode of trace_codes) {
        if (traceCode.trim()) {
          await run(
            `INSERT INTO 药品追溯码记录 (
              库存记录编号, 药品编号, 追溯码, 操作类型
            ) VALUES (?, ?, ?, ?)`,
            [inventoryId, product_id, traceCode.trim(), '入库']
          );
        }
      }
    }

    return NextResponse.json({
      success: true,
      message: '入库成功',
      data: {
        inventory_id: inventoryId,
        product_id: product_id,
        product_name: product.名称,
        quantity: quantity,
        previous_stock: currentStock,
        new_stock: newStock,
        trace_codes_count: trace_codes ? trace_codes.length : 0,
        upload_to_mashangfangxin: upload_to_mashangfangxin || false
      }
    });
  } catch (error) {
    console.error('入库操作失败:', error);
    return NextResponse.json({
      success: false,
      message: '入库操作失败: ' + (error instanceof Error ? error.message : '未知错误')
    }, { status: 500 });
  }
}